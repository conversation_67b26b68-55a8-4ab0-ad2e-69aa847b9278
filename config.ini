; Multi-Channel AI Service Configuration
; 多通道AI服务配置文件

[DEFAULT]
; Document Processing Settings
; 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

; Legacy compatibility (will be overridden by document processor)
INPUT_PDF = None
INPUT_FILE = tiku_md/tiku.md
OUTPUT_FILE = ankiflashcards.txt
LINES_TO_SKIP = 0

; Processing Settings (Character-based chunking)
; 处理设置（基于字符数的分块）
CHUNK_SIZE = 9000
CHUNK_STRIDE = 5000
; CHUNK_SIZE: 每个文本块的字符数 (12,000 characters per chunk)
; CHUNK_STRIDE: 滑动窗口步长 (9,500 characters step size, creating 2,500 character overlap)
; MAX_WORKERS 已移除 - 现在并发数由各通道的 MAX_CONCURRENT 总和决定

; Cache Settings
; 缓存设置
CACHE_DIR = cache

; File Naming Settings
; 文件命名设置
USE_TIMESTAMP = true
CLEAN_FILENAMES = true


; Content Processing Enhancement Settings
; 内容处理增强功能设置

; Content Validation Settings
; 内容验证设置
CONTENT_VALIDATION_THRESHOLD = 0.8
MAX_CONTENT_RETRIES = 2
ENABLE_CONTENT_VALIDATION = true
FAILED_BLOCKS_LOG = failed_blocks.log

; Two-Stage Processing Settings
; 两阶段处理设置
ENABLE_SMART_CHUNKING = true
PREPROCESSED_FILE_SUFFIX = _preprocessed.md
CHUNK_MARKER = <!-- CHUNK_BREAK -->

; AI-Driven Semantic Chunking Settings
; AI驱动的语义分块设置
ENABLE_AI_CHUNKING = true
AI_CHUNKING_TARGET_SIZE = 9000
; AI_CHUNKING_TARGET_SIZE = 12000
AI_CHUNKING_CONTEXT_WINDOW_SIZE = 2250
; AI_CHUNKING_CONTEXT_WINDOW_SIZE: AI分析的上下文窗口大小，默认为目标大小的1/4
AI_CHUNKING_MAX_CONTENT_SIZE = 20000
AI_CHUNKING_TIMEOUT = 180
AI_CHUNKING_FALLBACK_TO_RULES = true

; HTML Processing Settings
; HTML处理设置
ENABLE_HTML_OPTIMIZATION = true

; AI Channel 1 - DeepSeek (Primary)
; AI通道1 - DeepSeek（主要）
[CHANNEL_1]
API_BASE_URL = https://v2.voct.top/v1
API_PROVIDER = openai
API_KEY = fo-_to6t48Uxu76weXPT2rWE7U1mePzNdIF
MODEL_NAME = deepseek-ai/DeepSeek-V3
REQUEST_TIMEOUT = 120
WEIGHT = 2
ENABLED = true
MAX_CONCURRENT = 5
USE_STREAMING = true

; AI Channel 2 - Backup Channel (Disabled by default)
; AI通道2 - 备用通道（默认禁用）
[CHANNEL_2]
API_BASE_URL = https://tbai.xin/v1
API_PROVIDER = openai
API_KEY = sk-e1k1oXaoO4csLYek27JTOFiU3Ff1vYI1F0ytrY4xpnjSbrRe
MODEL_NAME = deepseek-v3
REQUEST_TIMEOUT = 120
WEIGHT = 1
ENABLED = true
MAX_CONCURRENT = 10
USE_STREAMING = true

; Environment Variable Examples:
; 环境变量示例：
; export CHANNEL_1_API_KEY="your_actual_key"
; export CHANNEL_2_ENABLED="true"
; export MAX_WORKERS="15"