#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_final_integration.py

最终集成测试 - 验证semantic_chunker.py与真实AI服务的集成
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_final_integration():
    """测试最终集成"""
    try:
        print("=== 最终集成测试 ===")
        
        # 1. 导入所有必要模块
        print("\n1. 导入模块...")
        from semantic_chunker import SemanticChunker
        from multi_channel_config import MultiChannelConfigParser
        from ai_service import AIService
        
        print("✓ 所有模块导入成功")
        
        # 2. 初始化配置系统
        print("\n2. 初始化配置系统...")
        parser = MultiChannelConfigParser()
        config = parser.get_config()
        
        print(f"✓ 配置加载成功，共 {len(config)} 个配置项")
        print(f"✓ AI通道数: {len(config.get('channels', []))}")
        
        # 3. 初始化AI服务
        print("\n3. 初始化AI服务...")
        ai_service = AIService(config)
        
        print(f"✓ AI服务初始化成功")
        print(f"✓ 可用通道数: {len(ai_service.channels)}")
        
        # 4. 初始化语义分块器
        print("\n4. 初始化语义分块器...")
        chunker = SemanticChunker(ai_service, config=config)
        
        print("✓ SemanticChunker初始化成功")
        print(f"✓ AI分块启用: {chunker.enable_ai_chunking}")
        print(f"✓ 目标块大小: {chunker.ai_chunking_target_size}")
        print(f"✓ 上下文窗口: {chunker.ai_chunking_context_window_size}")
        print(f"✓ 超时设置: {chunker.ai_chunking_timeout}s")
        
        # 5. 验证配置值正确性
        print("\n5. 验证配置值...")
        expected_target_size = config.get('ai_chunking_target_size', 12000)
        expected_context_window = config.get('ai_chunking_context_window_size', expected_target_size // 4)
        
        assert chunker.ai_chunking_target_size == expected_target_size, f"目标大小不匹配: {chunker.ai_chunking_target_size} != {expected_target_size}"
        assert chunker.ai_chunking_context_window_size == expected_context_window, f"上下文窗口不匹配: {chunker.ai_chunking_context_window_size} != {expected_context_window}"
        
        print("✓ 所有配置值验证通过")
        
        # 6. 测试配置动态读取
        print("\n6. 测试动态配置读取...")
        
        # 测试各种配置项
        test_configs = [
            'ai_chunking_target_size',
            'ai_chunking_context_window_size', 
            'ai_chunking_timeout',
            'enable_ai_chunking',
            'chunk_size',
            'chunk_stride',
            'base_dir'
        ]
        
        for config_key in test_configs:
            value = parser.get(config_key)
            print(f"  {config_key}: {value} ({type(value).__name__})")
        
        print("✓ 动态配置读取测试通过")
        
        print("\n=== 最终集成测试完成! ===")
        print("✅ 配置系统完全正常工作")
        print("✅ semantic_chunker.py 可以正确读取所有配置值")
        print("✅ 不再需要手动更新代码来添加新配置参数")
        print("✅ 系统现在支持动态配置加载")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_integration()
    if success:
        print("\n🎉 所有问题已解决!")
        print("现在可以在config.ini中添加任何新的配置参数，")
        print("系统会自动检测并加载，无需修改代码!")
    else:
        print("\n❌ 仍有问题需要解决")
