# Decision Log

## Decision 1: Project-Based File Organization (2025-06-14)
**Decision**: Implement project-based workspace structure with independent directories for each document
**Rationale**:
- Prevents file conflicts when processing multiple documents
- Enables better organization and management of processing artifacts
- Supports batch processing without interference between documents
- Allows for project-specific caching and configuration

**Implementation Details**:
- Created `documents/` workspace with subdirectories: source/, extracted/, anki/, cache/, logs/
- Each document creates unique project with timestamp-based naming
- Project isolation prevents resource conflicts and enables parallel processing
- Backward compatibility maintained with legacy single-file approach

## Decision 2: Unified Document Processor Architecture (2025-06-14)
**Decision**: Create single unified interface (document_processor.py) for all document formats
**Rationale**:
- Simplifies user experience with single entry point
- Enables consistent processing pipeline across formats
- Facilitates code reuse and maintenance
- Supports extensibility for new document formats

**Implementation Details**:
- DocumentProcessor class with format-specific extractors (PDF, Word, etc.)
- Automatic format detection based on file extensions
- Consistent API across all document types
- Modular extractor design for easy extension

## Decision 3: Intelligent File Naming Conventions (2025-06-14)
**Decision**: Implement standardized naming conventions with Windows compatibility
**Rationale**:
- Prevents file system conflicts and naming issues
- Ensures cross-platform compatibility, especially Windows
- Enables predictable file organization and retrieval
- Supports mixed document processing scenarios

**Implementation Details**:
- Project naming: `{cleaned_filename}_{timestamp}`
- Image naming: `{project}_{source}_{page:03d}_img_{index:03d}.png`
- Document name cleaning removes problematic characters: `<>:"/\|?*`
- Image folder naming: `images_{document_name}` for better organization

## Decision 4: Advanced Content Filtering System (2025-06-14)
**Decision**: Implement configurable image similarity detection for spam filtering
**Rationale**:
- Automatically removes advertising and unwanted images
- Improves content quality and reduces noise
- Configurable thresholds allow fine-tuning for different use cases
- Archive system enables review and threshold adjustment

**Implementation Details**:
- Multiple similarity algorithms: histogram comparison, perceptual hashing, template matching
- Configurable similarity thresholds (0.0-1.0) in filter_config.ini
- Filtered images archived with similarity scores for review
- Text content filtering for advertising keywords

## Decision 5: AI Integration with Streaming Support (2025-06-14)
**Decision**: Use OpenAI-compatible streaming APIs with concurrent processing
**Rationale**:
- Handles large documents efficiently without memory issues
- Concurrent processing improves performance for batch operations
- Streaming reduces latency and provides better user feedback
- OpenAI compatibility ensures broad API support

**Implementation Details**:
- Unified AIService module with streaming and non-streaming modes
- ThreadPoolExecutor for concurrent API calls with rate limiting
- Sliding window text processing with configurable chunk size
- Comprehensive caching and resume capability

## Decision 6: Cross-Chunk Validation System (2025-06-14)
**Decision**: Implement intelligent validation to handle content spanning multiple chunks
**Rationale**:
- Prevents data loss when questions/answers span processing boundaries
- Reduces false positives in missing content detection
- Enables selective regeneration for efficiency
- Improves overall content completeness and quality

**Implementation Details**:
- Smart image validation across sliding windows
- CSV reporting for problematic chunks requiring regeneration
- Enhanced AI prompts with stronger emphasis on content preservation
- Selective regeneration workflow for failed chunks only

## Decision 7: Fuzzy Project Matching for User Experience (2025-06-15)
**Decision**: Implement intelligent project matching with partial name support
**Rationale**:
- Significantly improves user experience by reducing typing requirements
- Reduces errors from exact name matching requirements
- Provides Git-like branch completion experience
- Maintains backward compatibility with exact matching

**Implementation Details**:
- Minimum 5-character requirement for fuzzy matching
- Multiple matching algorithms: prefix, contains, similarity scoring
- Interactive selection interface for multiple matches
- Enhanced error messages with user guidance

## Decision 8: Integrated Processing Pipeline (2025-06-15)
**Decision**: Integrate Anki generation directly into document processor
**Rationale**:
- Provides unified workflow instead of separate scripts
- Enables complete pipeline processing (Document → Markdown → Anki)
- Improves image management coordination between processing stages
- Reduces user complexity and potential for errors

**Implementation Details**:
- Multiple processing modes: --anki, --md-to-anki, --full
- Smart image copying with automatic path updates for Anki compatibility
- Code reuse from existing md_to_anki.py to avoid duplication
- Enhanced configuration system with AI parameters

## Decision 9: GUI Application Development Approach (2025-06-15)
**Decision**: Create GUI application while maintaining existing CLI as backend
**Rationale**:
- Provides user-friendly interface for non-technical users
- Maintains all existing functionality and backward compatibility
- Leverages proven CLI implementation as robust backend
- Enables both GUI and CLI usage patterns

**Implementation Details**:
- GUI as frontend layer calling existing document_processor.py
- Modern Python GUI framework (tkinter/PyQt to be decided)
- File/folder selection dialogs for user convenience
- Progress display and comprehensive error handling
- Windows file system compatibility maintained

## Decision 10: Unified GUI Architecture with Optional Enhancements (2025-06-15)
**Decision**: Implement single unified GUI application with intelligent feature detection for optional enhancements
**Rationale**:
- Simplifies maintenance by avoiding multiple GUI versions
- Provides progressive enhancement based on available dependencies
- Reduces user confusion with single entry point
- Enables graceful degradation when optional features unavailable
- Streamlines installation and deployment process

**Implementation Details**:
- Single document_processor_gui.py with smart initialization
- Automatic detection of tkinterdnd2 for drag-and-drop functionality
- Graceful fallback to core functionality when optional dependencies missing
- Consolidated requirements.txt for all dependencies including optional ones
- User-friendly messaging about available/unavailable features
- Unified documentation and usage instructions

**Critical Development Principle Established**:
- **Mandatory GUI Synchronization**: All future CLI feature additions must be immediately reflected in GUI
- **Feature Parity Requirement**: GUI application must maintain 100% functional equivalence with CLI
- **Development Workflow**: CLI implementation → GUI integration → Comprehensive testing → Documentation
- **User Experience Commitment**: GUI users deserve access to all system capabilities through the interface

2025-06-15 15:09:57 - Log of updates made.
2025-06-15 15:30:00 - Migrated decision history from .codelf and added GUI development decision
2025-06-15 16:04:02 - Added unified GUI architecture decision with optional enhancement strategy

## Decision 12: Multi-Template Prompt Management System (2025-06-15)
**Decision**: Implement comprehensive prompt management system supporting multiple template versions for different question bank formats
**Rationale**:
- Different question banks use varying answer formatting methods (参考答案, bold/red text, multiple choice formats)
- Future extensibility requires flexible prompt template system
- Command-line and GUI interfaces need unified prompt selection capabilities
- Backward compatibility must be maintained for existing workflows

**Implementation Details**:
- JSON-based prompt template storage with structured metadata
- Enum-based type safety for question types and answer formats
- Command-line parameters: --prompt <template_name> and --list-prompts
- GUI integration with dropdown selection and real-time template information
- Default template set to "current_complex" for optimal image processing support
- Comprehensive error handling and validation for invalid template selections
- Modular architecture enabling easy addition of new template types

## Decision 13: Source-Level Image Path Generation Strategy (2025-06-16)
**Decision**: Generate correct image paths directly during document processing instead of post-processing fixes
**Rationale**:
- Eliminates unnecessary string processing overhead and potential errors
- Provides cleaner, more maintainable code architecture
- Ensures consistency across all document formats (PDF, Word)
- Follows "fix at source" principle for better software design
- Improves performance by avoiding redundant processing steps

**Implementation Details**:
- Modified Word extractor to generate correct `images_{document_name}/filename` paths during image tag creation
- Updated PDF extractor to use same path generation logic for Markdown image references
- Integrated DocumentNameCleaner and ImageFolderManager for consistent naming
- Removed post-processing fix_image_paths_in_markdown calls from normal workflow
- Maintained fix function as utility for edge cases and migration scenarios

## Decision 14: HTML Span Element Merging for Content Quality (2025-06-16)
**Decision**: Implement intelligent merging of adjacent HTML span elements with identical style attributes
**Rationale**:
- Improves readability and reduces HTML bloat in generated Markdown
- Preserves semantic meaning while optimizing content structure
- Maintains exact whitespace preservation for accurate content representation
- Enhances Word-to-Markdown conversion quality without breaking existing functionality
- Provides foundation for future content optimization features

**Implementation Details**:
- Added merge_adjacent_spans method to FileManager class for centralized utility access
- Integrated merging into Word paragraph processing pipeline after formatting
- Used regex-based pattern matching with backreferences for style attribute comparison
- Implemented iterative merging to handle multiple consecutive spans with same styles
- Added comprehensive error handling and logging for debugging and monitoring
- Created extensive test suite covering edge cases and complex scenarios

## Decision 15: AI-Driven Semantic Chunking System Refactor (2025-06-16)
**Decision**: Completely refactor semantic chunking system from rule-based to AI-driven approach with comprehensive integration
**Rationale**:
- AI-driven analysis provides superior semantic boundary detection compared to rule-based heuristics
- Enables better understanding of document structure, question-answer pairs, and conceptual groupings
- Improves content quality and semantic completeness for downstream Anki generation
- Leverages existing multi-channel AI service architecture for reliability and performance
- Provides configurable and adaptive chunking strategies based on content characteristics

**Implementation Details**:
- Replaced rule-based `_intelligent_semantic_chunking()` with AI-powered `_ai_driven_semantic_chunking()`
- Created specialized AI prompts for semantic boundary analysis and chunk marker insertion
- Implemented content segmentation for handling documents larger than AI context limits
- Added comprehensive configuration system (target size, max content, timeout, fallback settings)
- Integrated robust error handling with automatic fallback to rule-based chunking
- Enhanced GUI interface with complete AI chunking configuration options
- Created extensive validation and testing framework for AI chunking functionality
- Maintained backward compatibility and seamless integration with existing document processing workflow