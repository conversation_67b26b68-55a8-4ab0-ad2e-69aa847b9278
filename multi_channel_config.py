#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_channel_config.py

多通道配置解析和管理模块
"""

import os
import logging
import configparser
import re
from typing import Dict, List, Any, Union, Optional
from pathlib import Path


class MultiChannelConfigParser:
    """多通道配置解析器 - 支持动态配置加载和类型推断"""

    def __init__(self, config_file: str = "config.ini"):
        """
        初始化配置解析器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_registry = {}  # 配置注册表，用于存储模块的配置需求
        self.config = self._load_config()

    def register_config_keys(self, module_name: str, config_spec: Dict[str, Dict[str, Any]]):
        """
        注册模块的配置需求

        Args:
            module_name: 模块名称
            config_spec: 配置规范，格式为 {key: {'default': value, 'type': type, 'description': str}}
        """
        self.config_registry[module_name] = config_spec
        logging.debug(f"注册模块 {module_name} 的配置需求: {list(config_spec.keys())}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持动态类型转换

        Args:
            key: 配置键名
            default: 默认值

        Returns:
            配置值，如果不存在则返回默认值
        """
        return self.config.get(key, default)

    def _infer_type_and_convert(self, value: str) -> Union[str, int, float, bool]:
        """
        自动推断类型并转换字符串值

        Args:
            value: 字符串值

        Returns:
            转换后的值
        """
        if not isinstance(value, str):
            return value

        value = value.strip()

        # 布尔值检测
        if value.lower() in ['true', 'yes', '1', 'on']:
            return True
        elif value.lower() in ['false', 'no', '0', 'off']:
            return False

        # 数字检测
        try:
            # 整数检测
            if '.' not in value and value.lstrip('-').isdigit():
                return int(value)
            # 浮点数检测
            elif re.match(r'^-?\d+\.\d+$', value):
                return float(value)
        except ValueError:
            pass

        # 默认返回字符串
        return value
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件 - 支持动态配置加载"""
        # 基础默认配置
        config = {
            # 基础文档处理设置
            'base_dir': 'documents',
            'pdf_top_margin': 50.0,
            'pdf_bottom_margin': 50.0,
            'chunk_size': 12000,      # 字符数 (characters)
            'chunk_stride': 9500,     # 字符数 (characters, creates 2500 char overlap)
            'lines_to_skip': 0,
            'cache_dir': 'cache',
            'use_timestamp': True,
            'clean_filenames': True,

            # 内容验证设置
            'content_validation_threshold': 0.8,
            'max_content_retries': 2,
            'enable_content_validation': True,
            'failed_blocks_log': 'failed_blocks.log',

            # 两阶段处理设置
            'enable_smart_chunking': True,
            'preprocessed_file_suffix': '_preprocessed.md',
            'chunk_marker': '<!-- CHUNK_BREAK -->',

            # AI驱动语义分块设置
            'enable_ai_chunking': True,
            'ai_chunking_target_size': 12000,
            'ai_chunking_context_window_size': 3000,  # 默认为目标大小的1/4
            'ai_chunking_max_content_size': 50000,
            'ai_chunking_timeout': 180,
            'ai_chunking_fallback_to_rules': True,

            # HTML处理设置
            'enable_html_optimization': True,

            # 多通道配置
            'channels': []
        }
        
        if not os.path.exists(self.config_file):
            logging.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
            return config

        try:
            parser = configparser.ConfigParser()
            parser.read(self.config_file, encoding="utf-8")

            # 动态加载DEFAULT section中的所有配置
            if 'DEFAULT' in parser:
                default_section = parser['DEFAULT']

                # 动态加载所有配置项
                for key, value in default_section.items():
                    # 转换配置键名为小写下划线格式
                    config_key = key.lower()

                    # 自动类型推断和转换
                    converted_value = self._infer_type_and_convert(value)
                    config[config_key] = converted_value

                    logging.debug(f"动态加载配置: {config_key} = {converted_value} ({type(converted_value).__name__})")

                # 特殊处理：如果没有设置AI_CHUNKING_CONTEXT_WINDOW_SIZE，使用目标大小的1/4
                if 'ai_chunking_context_window_size' not in config and 'ai_chunking_target_size' in config:
                    config['ai_chunking_context_window_size'] = config['ai_chunking_target_size'] // 4
                    logging.debug(f"自动计算 ai_chunking_context_window_size = {config['ai_chunking_context_window_size']}")
            
            # 检查是否有多通道配置
            channels = self._parse_channels(parser)
            if channels:
                config['channels'] = channels
                logging.info(f"加载了 {len(channels)} 个AI通道配置")
            else:
                # 向后兼容：尝试加载单通道配置
                single_channel = self._parse_single_channel(parser)
                if single_channel:
                    config['channels'] = [single_channel]
                    logging.info("加载了单通道配置（向后兼容模式）")
            
            # 环境变量覆盖
            self._apply_env_overrides(config)
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            logging.info("使用默认配置")
        
        return config
    
    def _parse_channels(self, parser: configparser.ConfigParser) -> List[Dict[str, Any]]:
        """解析多通道配置"""
        channels = []
        
        # 查找所有CHANNEL_开头的section
        for section_name in parser.sections():
            if section_name.startswith('CHANNEL_'):
                try:
                    section = parser[section_name]
                    
                    # 必需的配置项
                    required_keys = ['API_BASE_URL', 'API_KEY', 'MODEL_NAME']
                    if not all(key in section for key in required_keys):
                        logging.warning(f"通道 {section_name} 缺少必需配置，跳过")
                        continue
                    
                    channel_config = {
                        'name': section_name.lower(),
                        'api_base_url': section.get('API_BASE_URL').rstrip('/'),
                        'api_provider': section.get('API_PROVIDER', 'openai'),
                        'api_key': section.get('API_KEY'),
                        'model_name': section.get('MODEL_NAME'),
                        'request_timeout': int(section.get('REQUEST_TIMEOUT', 120)),
                        'weight': int(section.get('WEIGHT', 1)),
                        'enabled': section.getboolean('ENABLED', True),
                        'max_concurrent': int(section.get('MAX_CONCURRENT', 5)),
                        'use_streaming': section.getboolean('USE_STREAMING', True)
                    }
                    
                    channels.append(channel_config)
                    logging.debug(f"解析通道配置: {channel_config['name']}")
                    
                except Exception as e:
                    logging.error(f"解析通道 {section_name} 配置失败: {e}")
        
        return channels
    
    def _parse_single_channel(self, parser: configparser.ConfigParser) -> Dict[str, Any]:
        """解析单通道配置（向后兼容）"""
        try:
            if 'DEFAULT' not in parser:
                return None
            
            section = parser['DEFAULT']
            
            # 检查是否有AI相关配置
            if not all(key in section for key in ['API_BASE_URL', 'API_KEY', 'MODEL_NAME']):
                return None
            
            return {
                'name': 'default',
                'api_base_url': section.get('API_BASE_URL').rstrip('/'),
                'api_provider': section.get('API_PROVIDER', 'openai'),
                'api_key': section.get('API_KEY'),
                'model_name': section.get('MODEL_NAME'),
                'request_timeout': int(section.get('REQUEST_TIMEOUT', 120)),
                'weight': 1,
                'enabled': True,
                'max_concurrent': 5,
                'use_streaming': section.getboolean('USE_STREAMING', True)
            }
            
        except Exception as e:
            logging.error(f"解析单通道配置失败: {e}")
            return None
    
    def _apply_env_overrides(self, config: Dict[str, Any]):
        """应用环境变量覆盖 - 支持动态环境变量检测"""
        # 动态检测所有相关的环境变量
        for env_key, env_val in os.environ.items():
            # 检查是否是配置相关的环境变量
            config_key = env_key.lower()

            # 如果环境变量名对应配置中的键，则应用覆盖
            if config_key in config:
                try:
                    # 使用类型推断进行转换
                    converted_val = self._infer_type_and_convert(env_val)
                    config[config_key] = converted_val
                    logging.debug(f"环境变量覆盖: {config_key} = {converted_val}")
                except Exception as e:
                    logging.warning(f"环境变量 {env_key} 转换失败: {e}")

        # 保持向后兼容的特定环境变量映射
        legacy_env_mappings = {
            'CHUNK_SIZE': 'chunk_size',
            'CHUNK_STRIDE': 'chunk_stride',
            'CACHE_DIR': 'cache_dir',
            'ENABLE_AI_CHUNKING': 'enable_ai_chunking',
            'AI_CHUNKING_TARGET_SIZE': 'ai_chunking_target_size',
            'AI_CHUNKING_CONTEXT_WINDOW_SIZE': 'ai_chunking_context_window_size',
            'AI_CHUNKING_TIMEOUT': 'ai_chunking_timeout'
        }

        for env_key, config_key in legacy_env_mappings.items():
            env_val = os.getenv(env_key)
            if env_val:
                try:
                    converted_val = self._infer_type_and_convert(env_val)
                    config[config_key] = converted_val
                    logging.debug(f"传统环境变量覆盖: {config_key} = {converted_val}")
                except Exception as e:
                    logging.warning(f"环境变量 {env_key} 转换失败: {e}")
        
        # 通道特定的环境变量覆盖
        for channel in config.get('channels', []):
            channel_name = channel['name'].upper()
            
            # 支持的环境变量格式：CHANNEL_DEFAULT_API_KEY, CHANNEL_1_MODEL_NAME 等
            env_mappings = {
                f'CHANNEL_{channel_name}_API_BASE_URL': 'api_base_url',
                f'CHANNEL_{channel_name}_API_KEY': 'api_key',
                f'CHANNEL_{channel_name}_MODEL_NAME': 'model_name',
                f'CHANNEL_{channel_name}_API_PROVIDER': 'api_provider',
                f'CHANNEL_{channel_name}_REQUEST_TIMEOUT': 'request_timeout',
                f'CHANNEL_{channel_name}_WEIGHT': 'weight',
                f'CHANNEL_{channel_name}_ENABLED': 'enabled',
                f'CHANNEL_{channel_name}_MAX_CONCURRENT': 'max_concurrent'
            }
            
            for env_key, config_key in env_mappings.items():
                env_val = os.getenv(env_key)
                if env_val:
                    if config_key in ['request_timeout', 'weight', 'max_concurrent']:
                        channel[config_key] = int(env_val)
                    elif config_key == 'enabled':
                        channel[config_key] = env_val.lower() in ['true', '1', 'yes', 'on']
                    else:
                        channel[config_key] = env_val
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config
    
    def has_ai_channels(self) -> bool:
        """检查是否有可用的AI通道"""
        channels = self.config.get('channels', [])
        return len(channels) > 0 and any(ch.get('enabled', True) for ch in channels)
    
    def get_enabled_channels(self) -> List[Dict[str, Any]]:
        """获取启用的通道列表"""
        return [ch for ch in self.config.get('channels', []) if ch.get('enabled', True)]
    
    def validate_config(self) -> List[str]:
        """验证配置，返回错误信息列表"""
        errors = []
        
        channels = self.config.get('channels', [])
        if not channels:
            errors.append("没有配置任何AI通道")
            return errors
        
        for i, channel in enumerate(channels):
            channel_name = channel.get('name', f'channel_{i}')
            
            # 检查必需字段
            required_fields = ['api_base_url', 'api_key', 'model_name']
            for field in required_fields:
                if not channel.get(field):
                    errors.append(f"通道 {channel_name} 缺少必需字段: {field}")
            
            # 检查URL格式
            api_url = channel.get('api_base_url', '')
            if api_url and not (api_url.startswith('http://') or api_url.startswith('https://')):
                errors.append(f"通道 {channel_name} API_BASE_URL 格式无效: {api_url}")
            
            # 检查数值范围
            timeout = channel.get('request_timeout', 120)
            if not isinstance(timeout, int) or timeout <= 0:
                errors.append(f"通道 {channel_name} REQUEST_TIMEOUT 必须是正整数")
            
            weight = channel.get('weight', 1)
            if not isinstance(weight, int) or weight <= 0:
                errors.append(f"通道 {channel_name} WEIGHT 必须是正整数")
        
        return errors
    
    def create_sample_config(self, output_file: str = "config_sample.ini"):
        """创建示例配置文件"""
        sample_content = """# Multi-Channel AI Service Configuration
# 多通道AI服务配置文件

[DEFAULT]
# Document Processing Settings
# 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

# Processing Settings (Character-based chunking)
# 处理设置（基于字符数的分块）
CHUNK_SIZE = 12000
CHUNK_STRIDE = 9500
# CHUNK_SIZE: 每个文本块的字符数 (12,000 characters per chunk)
# CHUNK_STRIDE: 滑动窗口步长 (9,500 characters step size, creating 2,500 character overlap)
LINES_TO_SKIP = 13
# 注意：MAX_WORKERS 已移除，现在并发数由各通道的 MAX_CONCURRENT 总和决定

# Cache Settings
# 缓存设置
CACHE_DIR = cache

# File Naming Settings
# 文件命名设置
USE_TIMESTAMP = true
CLEAN_FILENAMES = true

# AI-Driven Semantic Chunking Settings
# AI驱动的语义分块设置
ENABLE_AI_CHUNKING = true
AI_CHUNKING_TARGET_SIZE = 12000
AI_CHUNKING_CONTEXT_WINDOW_SIZE = 3000
AI_CHUNKING_MAX_CONTENT_SIZE = 50000
AI_CHUNKING_TIMEOUT = 180
AI_CHUNKING_FALLBACK_TO_RULES = true

# HTML Processing Settings
# HTML处理设置
ENABLE_HTML_OPTIMIZATION = true

# AI Channel 1 - DeepSeek
# AI通道1 - DeepSeek
[CHANNEL_1]
API_BASE_URL = https://api.deepseek.com/v1
API_PROVIDER = openai
API_KEY = your_deepseek_api_key_here
MODEL_NAME = deepseek-chat
REQUEST_TIMEOUT = 120
WEIGHT = 2
ENABLED = true
MAX_CONCURRENT = 5

# AI Channel 2 - OpenAI
# AI通道2 - OpenAI
[CHANNEL_2]
API_BASE_URL = https://api.openai.com/v1
API_PROVIDER = openai
API_KEY = your_openai_api_key_here
MODEL_NAME = gpt-4
REQUEST_TIMEOUT = 120
WEIGHT = 1
ENABLED = true
MAX_CONCURRENT = 3

# AI Channel 3 - Custom Provider
# AI通道3 - 自定义提供商
[CHANNEL_3]
API_BASE_URL = https://your-custom-api.com/v1
API_PROVIDER = openai
API_KEY = your_custom_api_key_here
MODEL_NAME = custom-model-name
REQUEST_TIMEOUT = 180
WEIGHT = 1
ENABLED = false
MAX_CONCURRENT = 2

# Environment Variable Examples:
# 环境变量示例：
# export CHANNEL_1_API_KEY="your_actual_key"
# export CHANNEL_2_ENABLED="false"
# export CHUNK_SIZE="600"
"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            logging.info(f"示例配置文件已创建: {output_file}")
        except Exception as e:
            logging.error(f"创建示例配置文件失败: {e}")


def main():
    """测试配置解析器"""
    logging.basicConfig(level=logging.INFO)

    parser = MultiChannelConfigParser()
    config = parser.get_config()

    print("配置解析结果:")
    print(f"基础目录: {config.get('base_dir', 'documents')}")
    print(f"分块大小: {config.get('chunk_size', 12000)}")
    print(f"AI分块目标大小: {config.get('ai_chunking_target_size', 12000)}")
    print(f"AI分块上下文窗口: {config.get('ai_chunking_context_window_size', 3000)}")
    print(f"AI通道数量: {len(config.get('channels', []))}")

    for i, channel in enumerate(config.get('channels', []), 1):
        print(f"通道 {i}: {channel['name']} - {channel['model_name']} ({'启用' if channel['enabled'] else '禁用'})")

    # 显示动态加载的配置项
    print(f"\n动态加载的配置项总数: {len(config)}")

    # 验证配置
    errors = parser.validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n配置验证通过")

    # 创建示例配置
    parser.create_sample_config()


if __name__ == "__main__":
    main()
