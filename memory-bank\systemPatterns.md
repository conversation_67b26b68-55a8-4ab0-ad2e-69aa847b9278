# System Patterns

## Coding Patterns

### Two-Stage Processing Pattern
**Context**: Complex AI processing tasks that benefit from preprocessing and semantic analysis
**Implementation**:
```python
# Stage 1: AI Preprocessing
chunking_result = semantic_chunker.preprocess_document(content, project_name)
if chunking_result.success:
    # Stage 2: Process semantic chunks
    processing_result = chunk_processor.process_semantic_chunks(
        chunking_result.preprocessed_content, project_name, config
    )
```
**Benefits**: Better semantic completeness, improved content quality, cacheable preprocessing results
**Usage**: Applied in semantic chunking system for intelligent content division

### Multi-Channel Configuration Management Pattern
**Context**: Complex systems requiring multiple AI service channels with environment variable overrides
**Implementation**:
```python
def load_config(config_path: str = "config.ini") -> dict:
    """多通道配置加载模式 - 支持向后兼容和环境变量覆盖"""
    # 1. 设置默认配置
    config = {'base_dir': 'documents', 'channels': []}

    # 2. 解析多通道配置
    for section_name in parser.sections():
        if section_name.startswith('CHANNEL_'):
            channel_config = {
                'name': section_name.lower(),
                'api_base_url': section.get('API_BASE_URL').rstrip('/'),
                'api_key': section.get('API_KEY'),
                'enabled': section.getboolean('ENABLED', True)
            }
            channels.append(channel_config)

    # 3. 环境变量覆盖
    _apply_env_overrides(config)
    return config
```
**Benefits**: Flexible configuration, environment-specific overrides, backward compatibility
**Usage**: Applied in AI service configuration for multi-channel support

### AI Service Integration Pattern
**Context**: Specialized AI API calls requiring custom prompt handling and response processing
**Implementation**:
```python
def call_raw_api(self, content: str, system_prompt: str, task_prompt: str) -> str:
    """AI服务原始API调用模式 - 用于语义分块等特殊场景"""
    channel = self.select_channel()
    formatted_task_prompt = task_prompt.format(content=content)

    # Provider-specific payload construction
    if channel.api_provider.lower() == "siliconflow":
        payload = {"messages": [{"role": "user", "content": f"{system_prompt}\n\n{formatted_task_prompt}"}]}
    else:
        payload = {"messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": formatted_task_prompt}
        ]}

    # Execute with error handling and statistics
    resp = requests.post(url, headers=headers, json=payload, timeout=timeout)
    return self._parse_raw_response(resp)
```
**Benefits**: Provider flexibility, custom response handling, integrated error tracking
**Usage**: Applied in semantic chunking for AI-driven content analysis

### Validation and Retry Pattern
**Context**: AI service calls that need quality assurance and fault tolerance
**Implementation**:
```python
# Content validation with configurable thresholds
validation_result = content_validator.validate_content_length(input_text, output_text)
if not validation_result.is_valid and content_validator.should_retry(attempt_count):
    # Retry with enhanced logging
    failed_block_logger.log_failed_block(...)
```
**Benefits**: Improved content quality, automatic error recovery, comprehensive failure tracking
**Usage**: Integrated into AI service for content validation and retry logic

### Modular Design Principles
- **Single Responsibility**: Each module handles one specific task (document extraction, AI processing, file generation, project management)
- **High Cohesion, Low Coupling**: Related functions are centralized, reducing dependencies between modules
- **Stable Interfaces**: Expose stable interfaces externally while internal implementations can vary
- **Format Extensibility**: New document formats can be added without affecting existing functionality

### Naming Conventions
- **Snake Case**: Use snake_case for Python variables and functions
- **Semantic Naming**: Variable/function names should clearly express their purpose
- **Project Naming**: `{cleaned_filename}_{timestamp}` format for unique identification
- **Image Naming**: `{project}_{source}_{page:03d}_img_{index:03d}.png` for standardization
- **Image Folder Naming**: `images_{cleaned_document_name}` for Windows compatibility

### Error Handling Patterns
- **Graceful Degradation**: System continues processing other documents when one fails
- **Comprehensive Logging**: Detailed logging with different levels (DEBUG, INFO, WARNING, ERROR)
- **User-Friendly Messages**: Clear error messages with actionable guidance
- **Retry Mechanisms**: Exponential backoff for API calls and network operations
- **Optional Feature Fallback**: Intelligent detection and graceful fallback for missing optional dependencies

### Configuration Management
- **Centralized Config**: Use config.ini for all configurable parameters
- **Environment Variables**: Support environment variable overrides for sensitive data
- **Default Values**: Provide sensible defaults for all configuration options
- **Validation**: Validate configuration parameters at startup
- **Template Management**: JSON-based template storage with enum type safety and metadata support

## Architectural Patterns

### Document Processing Pipeline
```
Input Document → Format Detection → Extractor Selection → Content Processing → Output Generation
```
- **Format Detection**: Automatic identification based on file extensions
- **Extractor Pattern**: Separate extractor classes for each document format
- **Unified Interface**: Common API across all document types
- **Pipeline Processing**: Consistent processing flow regardless of input format

### GUI Application Architecture
```
Main Application → Feature Detection → Component Initialization → Event Handling → Backend Integration
```
- **Progressive Enhancement**: Core functionality + optional enhancements based on available dependencies
- **Smart Initialization**: Automatic detection of optional libraries with graceful fallback
- **Unified Entry Point**: Single application file with intelligent feature branching
- **Threaded Processing**: Background operations with real-time UI updates

### Prompt Management Architecture
```
Template Storage → Template Loading → Validation → Selection Interface → Runtime Application
```
- **JSON Template Storage**: Structured storage with metadata and type information
- **Enum Type Safety**: QuestionType and AnswerFormat enums for type validation
- **Dynamic Template Selection**: Runtime template switching with validation
- **Multi-Interface Support**: Unified template management for CLI and GUI interfaces
- **Backward Compatibility**: Graceful fallback to default templates when specified templates unavailable

### Project-Based Organization
```
documents/
├── source/           # Original documents
├── extracted/        # Processed content by project
│   └── [project]/
├── anki/            # Generated flashcards
├── cache/[project]/ # Project-specific cache
└── logs/            # Processing logs
```
- **Isolation**: Each project has independent workspace
- **Resource Management**: Proper cleanup and memory management
- **Caching Strategy**: Project-specific caching for resume capability
- **Lifecycle Management**: Complete project creation, processing, and cleanup

### AI Integration Pattern
```
Text Input → Character-Based Chunking → Concurrent Processing → Validation → Output Assembly
```
- **Character-Based Sliding Window**: 12,000 character chunks with 9,500 character stride (2,500 char overlap)
- **Intelligent Boundary Detection**: Smart text splitting avoiding word/sentence breaks
- **Concurrent Processing**: ThreadPoolExecutor for parallel API calls
- **Streaming Support**: Handle large responses efficiently
- **Cross-Chunk Validation**: Ensure completeness across processing boundaries

### AI-Driven Semantic Chunking Pattern
```
Content Analysis → AI Semantic Boundary Detection → Chunk Marker Insertion → Validation → Fallback Handling
```
- **AI-Powered Analysis**: Use specialized prompts to analyze document structure and semantic boundaries
- **Intelligent Marker Placement**: Insert `<!-- CHUNK_BREAK -->` markers at optimal semantic boundaries
- **Content Segmentation**: Handle large documents by processing in overlapping segments
- **Validation and Fallback**: Validate AI output and fallback to rule-based chunking if needed
- **Configuration-Driven**: Support configurable target chunk sizes, timeouts, and processing limits
- **Multi-Channel Integration**: Leverage existing multi-channel AI service architecture for reliability

### Content Chunking Pattern
```
Text Lines → Full Text Assembly → Character-Based Sliding Window → Boundary Detection → Chunk Assembly
```
- **Boundary Priority**: Paragraph breaks > Sentence endings > Line breaks > Word boundaries > Exact position
- **Overlap Management**: Configurable overlap size to maintain context continuity
- **Edge Case Handling**: Empty text, short content, and oversized content scenarios
- **Performance Optimization**: Single-pass processing with intelligent boundary search

### Content Filtering Architecture
```
Content Input → Similarity Detection → Threshold Comparison → Filter/Archive Decision
```
- **Template Matching**: Compare against known spam image templates
- **Multiple Algorithms**: Histogram, perceptual hashing, SSIM comparison
- **Configurable Thresholds**: Adjustable similarity thresholds (0.0-1.0)
- **Archive System**: Save filtered content with similarity scores for review

### Cache Progress Calculation Pattern
```
Markdown File → Chunk Calculation → Cache File Count → Progress Ratio → Status Display
```
- **Expected Chunk Calculation**: Use same sliding window logic as make_chunks function
- **Actual Cache Counting**: Count numeric-named JSON files in cache directory
- **Progress Ratio**: Calculate cached/total percentage with meaningful status messages
- **Smart Status Display**: Context-aware status messages (未缓存, X/Y (Z%), 已完成)

### Batch Operation Pattern
```
Multi-Selection → Confirmation Dialog → Background Processing → Progress Tracking → Result Summary
```
- **Multi-Select Support**: Extended selection mode with visual feedback
- **Batch Confirmation**: Show selected items with truncation for large lists
- **Concurrent Processing**: Process multiple items with individual error isolation
- **Progress Tracking**: Real-time progress updates with current item display
- **Result Aggregation**: Comprehensive success/failure statistics with detailed results

### Source-Level Path Generation Pattern
```
Document Processing → Path Component Generation → Direct Path Assembly → Content Generation
```
- **Early Path Resolution**: Generate correct paths during initial processing stages
- **Component-Based Assembly**: Use DocumentNameCleaner and ImageFolderManager for consistent naming
- **Format-Agnostic Logic**: Same path generation logic across PDF and Word extractors
- **Elimination of Post-Processing**: Avoid string manipulation after content generation
- **Performance Optimization**: Single-pass path generation reduces computational overhead

### HTML Content Optimization Pattern
```
Raw HTML Generation → Pattern Detection → Adjacent Element Merging → Optimized Output
```
- **Regex-Based Pattern Matching**: Use backreferences for identical attribute detection
- **Iterative Processing**: Multiple passes to handle consecutive elements with same attributes
- **Whitespace Preservation**: Maintain exact spacing and formatting during optimization
- **Safe Transformation**: Preserve semantic meaning while reducing markup complexity
- **Comprehensive Testing**: Edge case validation for nested and malformed HTML structures

## Testing Patterns

### Validation Testing
- **End-to-End Testing**: Complete pipeline validation with real documents
- **Format-Specific Testing**: Dedicated tests for each document format
- **Edge Case Testing**: Handle corrupted files, empty documents, large files
- **Performance Testing**: Validate processing speed and memory usage

### Content Quality Assurance
- **Image Preservation Testing**: Verify all images are correctly extracted and positioned
- **Format Preservation Testing**: Ensure original formatting is maintained in output
- **Cross-Chunk Validation**: Test content completeness across processing boundaries
- **Similarity Threshold Testing**: Validate content filtering accuracy
- **HTML Optimization Testing**: Verify span merging preserves content while improving structure
- **Path Generation Testing**: Validate correct image paths are generated from source

### Integration Testing
- **API Integration**: Test AI service integration with different endpoints
- **File System Testing**: Validate Windows compatibility and path handling
- **Configuration Testing**: Test various configuration combinations
- **Error Recovery Testing**: Validate system behavior under failure conditions

### User Experience Testing
- **CLI Interface Testing**: Validate all command-line options and error messages
- **Fuzzy Matching Testing**: Test project name matching with various inputs
- **Interactive Mode Testing**: Validate user interaction flows
- **Progress Reporting Testing**: Ensure accurate progress tracking and display

## Code Quality Standards

### Python Best Practices
- **PEP 8 Compliance**: Follow Python style guidelines
- **Type Hints**: Use type hints for better code documentation and IDE support
- **Docstrings**: Comprehensive documentation for all public functions and classes
- **Context Managers**: Use context managers for resource management

### Performance Optimization
- **Memory Management**: Efficient handling of large documents
- **Concurrent Processing**: Utilize multiple threads for I/O-bound operations
- **Caching Strategy**: Multi-level caching to avoid redundant processing
- **Resource Cleanup**: Proper cleanup of temporary files and resources

### Security Considerations
- **API Key Protection**: Never hardcode sensitive information
- **Input Validation**: Validate all user inputs and file paths
- **Path Traversal Prevention**: Secure file system access
- **Error Information Protection**: Avoid exposing sensitive data in error messages

2025-06-15 15:09:57 - Log of updates made.
2025-06-15 15:35:00 - Migrated system patterns and standards from .codelf directory
2025-06-15 16:04:02 - Added GUI application architecture patterns and progressive enhancement strategies