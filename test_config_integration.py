#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_config_integration.py

测试配置系统集成 - 验证semantic_chunker.py能够正确读取配置值
"""

import logging
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_config_integration():
    """测试配置系统集成"""
    try:
        print("=== 配置系统集成测试 ===")
        
        # 1. 测试MultiChannelConfigParser
        print("\n1. 测试MultiChannelConfigParser...")
        from multi_channel_config import MultiChannelConfigParser
        
        config_parser = MultiChannelConfigParser()
        config = config_parser.get_config()
        
        print(f"✓ 配置加载成功，共加载 {len(config)} 个配置项")
        
        # 2. 测试关键配置值
        print("\n2. 测试关键配置值...")
        
        ai_chunking_target_size = config.get('ai_chunking_target_size')
        ai_chunking_context_window_size = config.get('ai_chunking_context_window_size')
        
        print(f"✓ AI_CHUNKING_TARGET_SIZE: {ai_chunking_target_size} ({type(ai_chunking_target_size).__name__})")
        print(f"✓ AI_CHUNKING_CONTEXT_WINDOW_SIZE: {ai_chunking_context_window_size} ({type(ai_chunking_context_window_size).__name__})")
        
        # 验证类型
        assert isinstance(ai_chunking_target_size, int), f"ai_chunking_target_size应该是int类型，实际是{type(ai_chunking_target_size)}"
        assert isinstance(ai_chunking_context_window_size, int), f"ai_chunking_context_window_size应该是int类型，实际是{type(ai_chunking_context_window_size)}"
        
        print("✓ 配置值类型验证通过")
        
        # 3. 测试SemanticChunker初始化
        print("\n3. 测试SemanticChunker初始化...")
        
        # 创建模拟AI服务
        class MockAIService:
            def call_raw_api(self, content, system_prompt, task_prompt, timeout=180):
                return content + "\n<!-- CHUNK_BREAK -->\n"
        
        from semantic_chunker import SemanticChunker
        
        mock_ai_service = MockAIService()
        chunker = SemanticChunker(mock_ai_service, config=config)
        
        print(f"✓ SemanticChunker初始化成功")
        print(f"✓ AI分块启用状态: {chunker.enable_ai_chunking}")
        print(f"✓ 目标块大小: {chunker.ai_chunking_target_size}")
        print(f"✓ 上下文窗口大小: {chunker.ai_chunking_context_window_size}")
        print(f"✓ 超时设置: {chunker.ai_chunking_timeout}")
        
        # 验证配置值正确读取
        assert chunker.ai_chunking_target_size == ai_chunking_target_size, "目标块大小配置读取错误"
        assert chunker.ai_chunking_context_window_size == ai_chunking_context_window_size, "上下文窗口大小配置读取错误"
        
        print("✓ SemanticChunker配置值验证通过")
        
        # 4. 测试动态配置功能
        print("\n4. 测试动态配置功能...")
        
        # 测试get方法
        test_value = config_parser.get('ai_chunking_target_size', 999)
        assert test_value == ai_chunking_target_size, "get方法返回值错误"
        
        # 测试不存在的配置项
        default_value = config_parser.get('non_existent_key', 'default_value')
        assert default_value == 'default_value', "默认值返回错误"
        
        print("✓ 动态配置功能验证通过")
        
        # 5. 测试配置项总数
        print(f"\n5. 配置统计:")
        print(f"✓ 总配置项数: {len(config)}")
        print(f"✓ AI通道数: {len(config.get('channels', []))}")
        
        enabled_channels = [ch for ch in config.get('channels', []) if ch.get('enabled', True)]
        print(f"✓ 启用的通道数: {len(enabled_channels)}")
        
        print("\n=== 所有测试通过! ===")
        print("✓ 配置系统集成成功")
        print("✓ semantic_chunker.py 现在可以正确读取所有配置值")
        print("✓ 动态配置加载功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_integration()
    sys.exit(0 if success else 1)
