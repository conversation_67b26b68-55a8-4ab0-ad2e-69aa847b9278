# Product Context

## Project Overview
Multi-Document Question Bank Generation System - Complete multi-document processing pipeline: PDF/Word → Markdown → Anki flashcards. Extract text/images from various document formats, then intelligently convert structured question-answer content to Anki-compatible format using AI.

Enhanced system supporting PDF, Word documents with unified file management, project-based organization, and intelligent naming conventions. Features batch processing, advanced image handling, and smart cross-chunk validation.

**Current Version**: v2.2 Enhanced Document Processing System with improved file organization and Windows compatibility
**Development Model**: Solo Developer

## Project Goal
Transform documents (PDF, Word, Markdown, Text) into structured Anki flashcards through intelligent processing:
- Unified interface for processing multiple document formats
- Automate extraction of text and images from documents to structured Markdown
- Generate Anki flashcards from processed content using AI
- Maintain project-based organization with intelligent file management
- Support batch processing for multiple documents
- Ensure Windows file system compatibility

## Key Features
- **字符基础智能分块系统**: 基于字符数的内容分块处理，支持12,000字符块大小和2,500字符重叠，智能边界检测避免词语分割，解决长行导致的token限制问题
- **多提示词模板管理系统**: 支持多种题库类型和答案格式的提示词模板，包括参考答案格式、标红加粗格式、选择题模板等，可通过命令行参数或GUI界面灵活选择
- **Multi-Format Support**: PDF, Word (.docx/.doc), Markdown, Text files with full text and image extraction
- **Project-Based Organization**: Independent workspaces for each document preventing conflicts
- **Intelligent File Management**: Automatic naming conventions, conflict resolution, Windows-compatible file names
- **Advanced Image Processing**: Smart positioning, spam filtering, similarity detection, contextual placement
- **AI-Powered Anki Generation**: Convert structured content to flashcards with cross-chunk validation
- **Batch Processing**: Handle multiple documents simultaneously with progress tracking
- **Content Filtering**: Remove advertising images and unwanted content using configurable similarity thresholds
- **Smart Image Positioning**: Analyze spatial relationships between images and text for precise placement
- **Format Preservation**: Maintain original document formatting (colors, bold, italic) in Markdown output
- **Comprehensive GUI Interface**: User-friendly graphical interface with tabbed design for all operations
- **Interactive Project Management**: Browse, search, filter, and manage projects with visual interface
- **Threaded Processing**: Background processing with real-time progress updates and status reporting
- **Fuzzy Project Matching**: Intelligent project search with partial name matching and selection dialogs
- **Unified Drag-and-Drop Support**: Optional file/folder drag-and-drop functionality with automatic detection
- **Smart Dependency Management**: Intelligent feature detection with graceful fallback for missing dependencies
- **Streamlined Installation**: Single requirements.txt for all dependencies including optional GUI enhancements
- **Multi-Channel AI Service**: Native support for multiple AI providers with intelligent load balancing and fault tolerance
- **Channel Testing and Monitoring**: Real-time AI channel status monitoring and connectivity testing
- **Optimized Image Naming**: Simplified image naming format to reduce token consumption in AI requests
- **Cache Progress Display**: Intelligent cache progress calculation showing actual cached files vs expected chunks with ratio/percentage display
- **Batch Anki Generation**: Multi-select project management with one-click batch Anki flashcard generation for multiple projects
- **HTML Span Element Merging**: Intelligent merging of adjacent HTML span elements with identical style attributes during Word-to-Markdown conversion, preserving whitespace and improving content quality
- **Source-Level Image Path Generation**: Direct generation of correct image paths during document processing, eliminating the need for post-processing fixes and ensuring consistent `images_{document_name}/filename` format
- **AI Content Validation and Retry System**: Comprehensive content validation with configurable character count thresholds (default 80%), automatic retry mechanisms (max 2 retries), and structured failed block logging for quality assurance
- **AI-Driven Semantic Chunking System**: Complete refactored intelligent content chunking with AI-powered semantic analysis, replacing rule-based approach with two-stage processing architecture for superior content quality and semantic completeness
- **Two-Stage Semantic Chunking**: AI-driven intelligent content chunking system with Stage 1 AI preprocessing to insert semantic chunk markers and Stage 2 processing of semantic chunks for improved content quality and semantic completeness
- **Dynamic Configuration Management System**: Unified configuration system with automatic detection and loading of all configuration keys from INI files, intelligent type inference, environment variable support, and elimination of manual code updates for new configuration parameters
- **Case-Insensitive Configuration System**: Enhanced configuration management with case-insensitive key lookup, duplicate configuration consolidation, and unified configuration access across all modules

## Overall Architecture
```
Core Multi-Document Processing System (v2.2)
├── document_processor.py          # Unified multi-document processor with integrated Anki generation
├── file_manager.py                # Unified file naming and project management system
├── pdf_extractor.py               # Enhanced PDF document processor with smart image positioning
├── word_extractor.py              # Word document processor (.docx/.doc support)
├── content_filter.py              # Advanced content filtering with image similarity detection
├── ai_service.py                  # Unified AI service module with streaming support
├── anki_generator.py              # Modular Anki card generation utilities
├── document_utils.py              # Document name cleaning and image folder management utilities
├── project_matcher.py             # Intelligent project matching with fuzzy search
├── markdown_detector.py           # Smart markdown file detection and selection
└── user_feedback.py               # User feedback system with intelligent error diagnosis

Document Processing Workspace
├── documents/
│   ├── source/                    # Original document storage
│   ├── extracted/                 # Processed content by project
│   │   └── [project_name]/
│   │       ├── [document].md      # Extracted Markdown content
│   │       └── images_[doc_name]/ # Project-specific images with document name
│   ├── anki/                      # Generated Anki flashcards
│   ├── cache/[project]/           # Project-specific processing cache
│   └── logs/                      # Processing logs

Content Filtering System
├── filter_config.ini              # Content filtering configuration
├── trash_image/                   # Spam/unwanted image templates
├── filtered_images/               # Detected spam images with similarity scores
└── Testing Tools/
    ├── test_similarity_threshold.py
    ├── analyze_filtered_images.py
    └── check_missing_images.py
```

## Technology Stack
**Core Dependencies**:
- Python 3.x (3.8+ recommended)
- PyMuPDF (>=1.23.0): PDF parsing and image extraction
- python-docx (>=0.8.11): Word document processing (.docx/.doc support)
- requests (>=2.25.0): HTTP client for AI API calls
- configparser (>=5.0.0): Configuration management
- concurrent.futures: Parallel processing
- tqdm (>=4.60.0): Progress tracking
- pathlib (>=1.0.0): Modern file path handling

**Optional Dependencies**:
- Pillow (>=9.0.0): Enhanced image processing and format conversion
- opencv-python (>=4.5.0): Advanced image similarity detection
- numpy (>=1.21.0): Numerical operations for image processing

**AI Integration**:
- OpenAI-compatible APIs with streaming support
- Configurable model selection and endpoints
- Concurrent processing with rate limiting

## Target Users
- Students and educators processing textbooks and study materials
- Researchers converting documents to structured formats
- Content creators building question banks and flashcards
- Anyone needing to extract and organize content from multiple document formats

## Success Metrics
- Document processing accuracy and completeness (target: >95%)
- Image preservation rate across all formats
- Processing speed for large documents (2000+ pages tested)
- User satisfaction with generated Anki cards quality
- System reliability and error handling robustness
- Windows file system compatibility and naming conventions

2025-06-15 15:09:57 - Log of updates made will be appended as footnotes to the end of this file.
2025-06-15 15:15:00 - Migrated comprehensive project information from .codelf directory
2025-06-15 16:04:02 - New feature: Completed comprehensive GUI application with unified drag-and-drop support and integrated dependency management
[2025-06-16 12:03:00] - New feature: 实现文档处理系统的两个关键增强功能：HTML span元素合并优化和图片路径格式修复，提升Word文档到Markdown转换的质量和一致性
[2025-06-15 19:03:42] - New feature: 完成提示词管理系统重构，支持多种提示词版本和不同问题库格式，包括命令行参数选择和GUI界面集成
[2025-06-15 20:42:50] - New feature: 实现缓存进度显示功能，替换简单布尔值为详细进度信息
[2025-06-15 21:00:46] - New feature: 实现项目管理批量生成Anki功能，支持多选项目一键批量生成闪卡
[2025-06-15 21:30:17] - New feature: 实现字符基础内容分块功能，替换行数分块解决token限制问题
[2025-06-17 17:39:42] - New feature: 完成配置管理系统重复配置合并和大小写敏感问题的彻底解决，实现真正的统一配置管理
[2025-06-17 12:27:21] - New feature: 完成多通道配置系统动态配置加载功能重构，解决semantic_chunker.py配置读取问题，实现真正的动态配置管理
[2025-06-16 22:29:19] - New feature: 完成semantic_chunker.py的AI服务集成和配置系统更新，语义分块功能测试通过
[2025-06-16 17:19:33] - New feature: 完成AI驱动智能内容分块系统重构，实现两阶段处理架构，替换规则驱动为AI驱动的语义分块，集成多通道AI服务，添加GUI配置界面，包含完整的错误处理和回退机制
[2025-06-16 13:32:03] - New feature: 完成Phase 2: AI驱动的智能内容分块（两阶段处理）系统实现