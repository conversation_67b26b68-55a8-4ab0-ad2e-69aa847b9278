# Multi-Channel AI Service Configuration
# 多通道AI服务配置文件

[DEFAULT]
# Document Processing Settings
# 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

# Processing Settings (Character-based chunking)
# 处理设置（基于字符数的分块）
CHUNK_SIZE = 12000
CHUNK_STRIDE = 9500
# CHUNK_SIZE: 每个文本块的字符数 (12,000 characters per chunk)
# CHUNK_STRIDE: 滑动窗口步长 (9,500 characters step size, creating 2,500 character overlap)
LINES_TO_SKIP = 13
# 注意：MAX_WORKERS 已移除，现在并发数由各通道的 MAX_CONCURRENT 总和决定

# Cache Settings
# 缓存设置
CACHE_DIR = cache

# File Naming Settings
# 文件命名设置
USE_TIMESTAMP = true
CLEAN_FILENAMES = true

# AI-Driven Semantic Chunking Settings
# AI驱动的语义分块设置
ENABLE_AI_CHUNKING = true
AI_CHUNKING_TARGET_SIZE = 12000
AI_CHUNKING_CONTEXT_WINDOW_SIZE = 3000
AI_CHUNKING_MAX_CONTENT_SIZE = 50000
AI_CHUNKING_TIMEOUT = 180
AI_CHUNKING_FALLBACK_TO_RULES = true

# HTML Processing Settings
# HTML处理设置
ENABLE_HTML_OPTIMIZATION = true

# AI Channel 1 - DeepSeek
# AI通道1 - DeepSeek
[CHANNEL_1]
API_BASE_URL = https://api.deepseek.com/v1
API_PROVIDER = openai
API_KEY = your_deepseek_api_key_here
MODEL_NAME = deepseek-chat
REQUEST_TIMEOUT = 120
WEIGHT = 2
ENABLED = true
MAX_CONCURRENT = 5

# AI Channel 2 - OpenAI
# AI通道2 - OpenAI
[CHANNEL_2]
API_BASE_URL = https://api.openai.com/v1
API_PROVIDER = openai
API_KEY = your_openai_api_key_here
MODEL_NAME = gpt-4
REQUEST_TIMEOUT = 120
WEIGHT = 1
ENABLED = true
MAX_CONCURRENT = 3

# AI Channel 3 - Custom Provider
# AI通道3 - 自定义提供商
[CHANNEL_3]
API_BASE_URL = https://your-custom-api.com/v1
API_PROVIDER = openai
API_KEY = your_custom_api_key_here
MODEL_NAME = custom-model-name
REQUEST_TIMEOUT = 180
WEIGHT = 1
ENABLED = false
MAX_CONCURRENT = 2

# Environment Variable Examples:
# 环境变量示例：
# export CHANNEL_1_API_KEY="your_actual_key"
# export CHANNEL_2_ENABLED="false"
# export CHUNK_SIZE="600"
